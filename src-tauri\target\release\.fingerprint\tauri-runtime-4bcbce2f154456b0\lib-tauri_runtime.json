{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 5617955072959260810, "deps": [[442785307232013896, "build_script_build", false, 4323013092030995279], [3150220818285335163, "url", false, 4415034812604799782], [4143744114649553716, "raw_window_handle", false, 16225817570159418652], [7606335748176206944, "dpi", false, 14606511730787558280], [9010263965687315507, "http", false, 9745525180057106504], [9689903380558560274, "serde", false, 15866268910020275570], [10806645703491011684, "thiserror", false, 11504436264092613361], [11050281405049894993, "tauri_utils", false, 13681551877324909697], [13116089016666501665, "windows", false, 5463830761823751857], [15367738274754116744, "serde_json", false, 12735670442722202607], [16727543399706004146, "cookie", false, 8719520066357431755]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-4bcbce2f154456b0\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}