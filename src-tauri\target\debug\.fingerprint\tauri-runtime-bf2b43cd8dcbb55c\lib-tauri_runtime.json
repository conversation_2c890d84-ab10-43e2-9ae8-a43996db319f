{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 5617955072959260810, "deps": [[442785307232013896, "build_script_build", false, 12921711006944267673], [3150220818285335163, "url", false, 10901662651222008182], [4143744114649553716, "raw_window_handle", false, 2771300590775076479], [7606335748176206944, "dpi", false, 2056858063435270774], [9010263965687315507, "http", false, 16714832165723395293], [9689903380558560274, "serde", false, 17399218494941812765], [10806645703491011684, "thiserror", false, 11478485744550063485], [11050281405049894993, "tauri_utils", false, 12727919046660084077], [13116089016666501665, "windows", false, 111976557147647525], [15367738274754116744, "serde_json", false, 5842069726811623790], [16727543399706004146, "cookie", false, 6638832591602858580]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-bf2b43cd8dcbb55c\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}