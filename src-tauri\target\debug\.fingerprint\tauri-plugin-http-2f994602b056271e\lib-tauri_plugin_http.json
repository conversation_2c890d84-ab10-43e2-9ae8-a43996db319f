{"rustc": 16591470773350601817, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 893707485121594417, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 12982635110730627093], [3150220818285335163, "url", false, 10901662651222008182], [8298091525883606470, "cookie_store", false, 18372245514245684669], [9010263965687315507, "http", false, 16714832165723395293], [9451456094439810778, "regex", false, 17734066832936030513], [9538054652646069845, "tokio", false, 11510013571635662308], [9689903380558560274, "serde", false, 17399218494941812765], [10755362358622467486, "tauri", false, 12606124176865590564], [10806645703491011684, "thiserror", false, 11478485744550063485], [13890802266741835355, "tauri_plugin_fs", false, 4205139602889693531], [15367738274754116744, "serde_json", false, 5842069726811623790], [15441187897486245138, "build_script_build", false, 8099093360412427420], [16066129441945555748, "bytes", false, 14894235019656734387], [16593743942913858012, "reqwest", false, 11263143439881442446], [17047088963840213854, "data_url", false, 8078720425069678142]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-2f994602b056271e\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}