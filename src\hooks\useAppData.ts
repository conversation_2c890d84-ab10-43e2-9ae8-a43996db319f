import { useState, useEffect, useCallback } from 'react';
import { AppData, Playlist, Mix, NotificationState } from '../types';
import { appDataApi, playlistApi, queueApi, mixApi } from '../utils/tauri';
import { fetchYouTubePlaylist, combinePlaylistsForMix, extractPlaylistIds } from '../utils/youtube';

/**
 * Custom hook for managing app data state
 */
export const useAppData = () => {
  const [appData, setAppData] = useState<AppData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState<NotificationState | null>(null);

  /**
   * Load app data from backend
   */
  const loadAppData = useCallback(async () => {
    try {
      const data = await appDataApi.load();
      setAppData(data);
      return data;
    } catch (error) {
      console.error('Failed to load app data:', error);
      showNotification('Failed to load app data', 'error');
      return null;
    }
  }, []);

  /**
   * Show notification with auto-dismiss
   */
  const showNotification = useCallback((message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  }, []);

  /**
   * Add a new playlist
   */
  const addPlaylist = useCallback(async (playlistUrl: string): Promise<boolean> => {
    if (!playlistUrl.trim()) {
      showNotification('Please enter a playlist URL', 'error');
      return false;
    }

    setIsLoading(true);
    try {
      console.log('Starting playlist addition process for URL:', playlistUrl);

      // Extract playlist ID using frontend implementation
      const playlistIds = extractPlaylistIds(playlistUrl.trim());
      console.log('Extracted playlist IDs:', playlistIds);

      if (playlistIds.length === 0) {
        throw new Error('Invalid YouTube playlist URL. Please make sure the URL contains a valid playlist ID.');
      }

      const playlistId = playlistIds[0]; // Take the first valid playlist ID
      console.log('Using playlist ID:', playlistId);

      // Check if playlist already exists
      if (appData?.playlists[playlistId]) {
        throw new Error('This playlist has already been added.');
      }

      // Fetch playlist data using frontend YouTube API
      console.log('Fetching playlist data...');
      const playlist = await fetchYouTubePlaylist(playlistId);
      console.log('Fetched playlist:', playlist);

      // Add to backend
      console.log('Adding playlist to backend...');
      await playlistApi.add(playlist);

      // Reload app data
      console.log('Reloading app data...');
      await loadAppData();

      showNotification(`Added "${playlist.title}" with ${playlist.video_count} videos successfully!`, 'success');
      return true;
    } catch (error) {
      console.error('Failed to add playlist:', error);

      let errorMessage = 'Failed to add playlist. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      showNotification(errorMessage, 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [appData, loadAppData, showNotification]);

  /**
   * Remove a playlist
   */
  const removePlaylist = useCallback(async (playlistId: string): Promise<boolean> => {
    try {
      await playlistApi.remove(playlistId);
      await loadAppData();
      showNotification('Playlist removed successfully', 'success');
      return true;
    } catch (error) {
      console.error('Failed to remove playlist:', error);
      showNotification('Failed to remove playlist', 'error');
      return false;
    }
  }, [loadAppData, showNotification]);

  /**
   * Refresh a playlist (re-fetch from YouTube)
   */
  const refreshPlaylist = useCallback(async (playlistId: string): Promise<boolean> => {
    if (!appData) return false;

    const existingPlaylist = appData.playlists[playlistId];
    if (!existingPlaylist) {
      showNotification('Playlist not found', 'error');
      return false;
    }

    setIsLoading(true);
    try {
      // Re-fetch playlist data
      const updatedPlaylist = await fetchYouTubePlaylist(playlistId);

      // Preserve original metadata
      updatedPlaylist.created_at = existingPlaylist.created_at;

      // Update in backend
      await playlistApi.add(updatedPlaylist);

      // Reload app data
      await loadAppData();

      showNotification(`Refreshed "${updatedPlaylist.title}" successfully!`, 'success');
      return true;
    } catch (error) {
      console.error('Failed to refresh playlist:', error);
      showNotification('Failed to refresh playlist', 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [appData, loadAppData, showNotification]);

  /**
   * Create a new mix
   */
  const createMix = useCallback(async (name: string, playlistIds: string[]): Promise<boolean> => {
    if (!name.trim()) {
      showNotification('Please enter a mix name', 'error');
      return false;
    }

    if (playlistIds.length === 0) {
      showNotification('Please select at least one playlist', 'error');
      return false;
    }

    try {
      await mixApi.create(name, playlistIds);
      await loadAppData();
      showNotification(`Created mix "${name}" successfully!`, 'success');
      return true;
    } catch (error) {
      console.error('Failed to create mix:', error);
      showNotification('Failed to create mix', 'error');
      return false;
    }
  }, [loadAppData, showNotification]);

  /**
   * Set current queue from playlist
   */
  const setQueueFromPlaylist = useCallback(async (playlist: Playlist, shuffle: boolean = false): Promise<boolean> => {
    try {
      await queueApi.set(playlist.videos, shuffle);
      await loadAppData();
      return true;
    } catch (error) {
      console.error('Failed to set queue from playlist:', error);
      showNotification('Failed to play playlist', 'error');
      return false;
    }
  }, [loadAppData, showNotification]);

  /**
   * Set current queue from mix
   */
  const setQueueFromMix = useCallback(async (mix: Mix, shuffle: boolean = false): Promise<boolean> => {
    if (!appData) return false;

    try {
      const videos = combinePlaylistsForMix(mix.playlist_ids, appData.playlists);
      await queueApi.set(videos, shuffle);
      await loadAppData();
      return true;
    } catch (error) {
      console.error('Failed to set queue from mix:', error);
      showNotification('Failed to play mix', 'error');
      return false;
    }
  }, [appData, loadAppData, showNotification]);

  /**
   * Set current video index
   */
  const setCurrentIndex = useCallback(async (index: number): Promise<boolean> => {
    try {
      await queueApi.setIndex(index);
      await loadAppData();
      return true;
    } catch (error) {
      console.error('Failed to set current index:', error);
      return false;
    }
  }, [loadAppData]);

  /**
   * Shuffle current queue
   */
  const shuffleQueue = useCallback(async (): Promise<boolean> => {
    if (!appData || appData.current_queue.length === 0) return false;

    try {
      await queueApi.set(appData.current_queue, true);
      await loadAppData();
      return true;
    } catch (error) {
      console.error('Failed to shuffle queue:', error);
      showNotification('Failed to shuffle queue', 'error');
      return false;
    }
  }, [appData, loadAppData, showNotification]);

  // Load app data on mount
  useEffect(() => {
    loadAppData();
  }, [loadAppData]);

  return {
    appData,
    isLoading,
    notification,
    showNotification,
    loadAppData,
    addPlaylist,
    removePlaylist,
    refreshPlaylist,
    createMix,
    setQueueFromPlaylist,
    setQueueFromMix,
    setCurrentIndex,
    shuffleQueue
  };
};
