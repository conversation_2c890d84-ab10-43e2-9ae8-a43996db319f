{"rustc": 16591470773350601817, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 16059508677036159627, "deps": [[2883436298747778685, "pki_types", false, 11300708519298859661], [3722963349756955755, "once_cell", false, 11157636835710291108], [5491919304041016563, "ring", false, 13590638768229720639], [6528079939221783635, "zeroize", false, 11758984375569177388], [7161480121686072451, "build_script_build", false, 3197060113118917682], [17003143334332120809, "subtle", false, 6281776158362536303], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 9520412564052768181]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-ca627544b805b532\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}