{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2040997289075261528, "path": 16836410838408385390, "deps": [[500211409582349667, "shared_child", false, 11220590799088304655], [1582828171158827377, "build_script_build", false, 4752117984449868822], [5986029879202738730, "log", false, 7548405875655827093], [9451456094439810778, "regex", false, 605544959772061804], [9538054652646069845, "tokio", false, 16960573143471379461], [9689903380558560274, "serde", false, 15866268910020275570], [10755362358622467486, "tauri", false, 1320775132830790491], [10806645703491011684, "thiserror", false, 11504436264092613361], [11337703028400419576, "os_pipe", false, 14159984232835274701], [14564311161534545801, "encoding_rs", false, 2851733786934483033], [15367738274754116744, "serde_json", false, 12735670442722202607], [16192041687293812804, "open", false, 6755281167607265935]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-shell-40b140e2553993c0\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}