{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 15121993098081410364, "deps": [[561782849581144631, "html5ever", false, 5515846595870150940], [1200537532907108615, "url<PERSON><PERSON>n", false, 12982635110730627093], [3129130049864710036, "memchr", false, 8855540930504683476], [3150220818285335163, "url", false, 10901662651222008182], [3191507132440681679, "serde_untagged", false, 13181491170388828874], [4899080583175475170, "semver", false, 8023696981330402074], [5986029879202738730, "log", false, 14896891622662359858], [6213549728662707793, "serde_with", false, 15100316989687599966], [6262254372177975231, "kuchiki", false, 11997340035213929671], [6606131838865521726, "ctor", false, 9242899358425018025], [7170110829644101142, "json_patch", false, 12618711167511611433], [8319709847752024821, "uuid", false, 7566486340186639533], [8786711029710048183, "toml", false, 4460435783043807171], [9010263965687315507, "http", false, 16714832165723395293], [9451456094439810778, "regex", false, 17734066832936030513], [9689903380558560274, "serde", false, 17399218494941812765], [10806645703491011684, "thiserror", false, 11478485744550063485], [11989259058781683633, "dunce", false, 9940206787301489372], [13625485746686963219, "anyhow", false, 2778989786511573041], [14132538657330703225, "brotli", false, 17868646577405114960], [15367738274754116744, "serde_json", false, 5842069726811623790], [15622660310229662834, "walkdir", false, 11031336991057513661], [17146114186171651583, "infer", false, 7771177705503510084], [17155886227862585100, "glob", false, 17739493393613972577], [17186037756130803222, "phf", false, 14042279231354029653]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-f64751066aca27fd\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}