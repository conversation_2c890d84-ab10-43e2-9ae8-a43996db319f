{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 15121993098081410364, "deps": [[561782849581144631, "html5ever", false, 3555528774483933149], [1200537532907108615, "url<PERSON><PERSON>n", false, 3113689551287369002], [3129130049864710036, "memchr", false, 7799890594497654866], [3150220818285335163, "url", false, 4415034812604799782], [3191507132440681679, "serde_untagged", false, 2732278569809858471], [4899080583175475170, "semver", false, 17036466027990893057], [5986029879202738730, "log", false, 7548405875655827093], [6213549728662707793, "serde_with", false, 7132946482980428234], [6262254372177975231, "kuchiki", false, 3239592564283695196], [6606131838865521726, "ctor", false, 1365014489633018958], [7170110829644101142, "json_patch", false, 11829559416439672971], [8319709847752024821, "uuid", false, 9226187276590073545], [8786711029710048183, "toml", false, 4367525871524959396], [9010263965687315507, "http", false, 9745525180057106504], [9451456094439810778, "regex", false, 605544959772061804], [9689903380558560274, "serde", false, 15866268910020275570], [10806645703491011684, "thiserror", false, 11504436264092613361], [11989259058781683633, "dunce", false, 18200519651017601263], [13625485746686963219, "anyhow", false, 5041847958751135102], [14132538657330703225, "brotli", false, 14480526960984598807], [15367738274754116744, "serde_json", false, 12735670442722202607], [15622660310229662834, "walkdir", false, 16413382258842205346], [17146114186171651583, "infer", false, 3929289105780917624], [17155886227862585100, "glob", false, 12483713391891018660], [17186037756130803222, "phf", false, 8651844151455544663]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-dfce7ea907919082\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}