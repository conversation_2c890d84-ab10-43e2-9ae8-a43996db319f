# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-outer-position"
description = "Enables the outer_position command without any pre-configured scope."
commands.allow = ["outer_position"]

[[permission]]
identifier = "deny-outer-position"
description = "Denies the outer_position command without any pre-configured scope."
commands.deny = ["outer_position"]
