{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"h2\", \"http2\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 8699810217270219721, "deps": [[40386456601120721, "percent_encoding", false, 11774129644120267593], [95042085696191081, "ipnet", false, 3432425162136660832], [418947936956741439, "h2", false, 10639548652959674658], [784494742817713399, "tower_service", false, 16090547528037016548], [1906322745568073236, "pin_project_lite", false, 4448338548239841136], [2517136641825875337, "sync_wrapper", false, 3867994030965247911], [2883436298747778685, "rustls_pki_types", false, 17462891765331675105], [3150220818285335163, "url", false, 10901662651222008182], [3722963349756955755, "once_cell", false, 196681822214180401], [5340155882212614564, "hyper_util", false, 1353141960172926839], [5695049318159433696, "tower", false, 3208774088850601272], [5986029879202738730, "log", false, 14896891622662359858], [7161480121686072451, "rustls", false, 11054030527282372755], [7620660491849607393, "futures_core", false, 4969618709217904929], [8156804143951879168, "webpki_roots", false, 4590427057622080856], [8298091525883606470, "cookie_store", false, 18372245514245684669], [9010263965687315507, "http", false, 16714832165723395293], [9538054652646069845, "tokio", false, 11510013571635662308], [9689903380558560274, "serde", false, 17399218494941812765], [10229185211513642314, "mime", false, 16676086155055312703], [10629569228670356391, "futures_util", false, 15232469423436694276], [11895591994124935963, "tokio_rustls", false, 14410619681330763860], [11957360342995674422, "hyper", false, 627882191474178816], [13077212702700853852, "base64", false, 18022071868568469129], [13330729881774643389, "hyper_rustls", false, 13981638722690614376], [14084095096285906100, "http_body", false, 4026790365637651394], [14564311161534545801, "encoding_rs", false, 14126388641811842202], [15032952994102373905, "rustls_pemfile", false, 17794415867612093491], [15697835491348449269, "windows_registry", false, 17670854093015113313], [16066129441945555748, "bytes", false, 14894235019656734387], [16542808166767769916, "serde_urlencoded", false, 10752398439976964219], [16727543399706004146, "cookie_crate", false, 6638832591602858580], [16900715236047033623, "http_body_util", false, 11964135788357577573]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-df014de83f9e392f\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}