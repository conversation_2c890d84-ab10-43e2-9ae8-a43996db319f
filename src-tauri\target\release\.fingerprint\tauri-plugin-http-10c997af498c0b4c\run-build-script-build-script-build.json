{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 11794583704759428693], [13890802266741835355, "build_script_build", false, 10690517429534199910], [15441187897486245138, "build_script_build", false, 17445198593712687320]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-http-10c997af498c0b4c\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}