{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[791294565694001944, "build_script_build", false, 12109071656287121702], [10755362358622467486, "build_script_build", false, 11794583704759428693], [13890802266741835355, "build_script_build", false, 10690517429534199910], [15441187897486245138, "build_script_build", false, 11509775261427612086], [3935545708480822364, "build_script_build", false, 4735662911052403905], [1582828171158827377, "build_script_build", false, 4752117984449868822]], "local": [{"RerunIfChanged": {"output": "release\\build\\lightning-shuffler-a4450b24bbc07d4d\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}