{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[791294565694001944, "build_script_build", false, 17337969265395199603], [10755362358622467486, "build_script_build", false, 384261636170580466], [13890802266741835355, "build_script_build", false, 1349321380448695464], [15441187897486245138, "build_script_build", false, 8099093360412427420], [3935545708480822364, "build_script_build", false, 7407884043414431393], [1582828171158827377, "build_script_build", false, 9192057976439078898]], "local": [{"RerunIfChanged": {"output": "debug\\build\\lightning-shuffler-10c5d3e01414476c\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}