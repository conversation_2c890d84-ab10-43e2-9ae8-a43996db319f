{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 17114967381396467289, "deps": [[376837177317575824, "build_script_build", false, 8105534359080525252], [4143744114649553716, "raw_window_handle", false, 2771300590775076479], [5986029879202738730, "log", false, 14896891622662359858], [10281541584571964250, "windows_sys", false, 13956739541983127240]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-16f670f970e991a1\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}