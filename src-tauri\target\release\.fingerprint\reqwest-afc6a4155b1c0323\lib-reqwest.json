{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"h2\", \"http2\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 8699810217270219721, "deps": [[40386456601120721, "percent_encoding", false, 8048334745010741628], [95042085696191081, "ipnet", false, 14424288666543982792], [418947936956741439, "h2", false, 140891369832744923], [784494742817713399, "tower_service", false, 14062531496293158253], [1906322745568073236, "pin_project_lite", false, 11319843651176751956], [2517136641825875337, "sync_wrapper", false, 17660106248538796819], [2883436298747778685, "rustls_pki_types", false, 11300708519298859661], [3150220818285335163, "url", false, 4415034812604799782], [3722963349756955755, "once_cell", false, 11157636835710291108], [5340155882212614564, "hyper_util", false, 17867795535085641452], [5695049318159433696, "tower", false, 11640537954619833737], [5986029879202738730, "log", false, 7548405875655827093], [7161480121686072451, "rustls", false, 2638966865526505037], [7620660491849607393, "futures_core", false, 7654422448818864904], [8156804143951879168, "webpki_roots", false, 3723774339715123144], [8298091525883606470, "cookie_store", false, 4078074362396950370], [9010263965687315507, "http", false, 9745525180057106504], [9538054652646069845, "tokio", false, 16960573143471379461], [9689903380558560274, "serde", false, 15866268910020275570], [10229185211513642314, "mime", false, 14694045163818787355], [10629569228670356391, "futures_util", false, 16259651420667836379], [11895591994124935963, "tokio_rustls", false, 10649951221120548780], [11957360342995674422, "hyper", false, 12532915195715325644], [13077212702700853852, "base64", false, 5159301121394365563], [13330729881774643389, "hyper_rustls", false, 16998610257082044533], [14084095096285906100, "http_body", false, 12457734925858900016], [14564311161534545801, "encoding_rs", false, 2851733786934483033], [15032952994102373905, "rustls_pemfile", false, 6772100798090991968], [15697835491348449269, "windows_registry", false, 3999741590699669425], [16066129441945555748, "bytes", false, 6037974485521700484], [16542808166767769916, "serde_urlencoded", false, 2251029225472315398], [16727543399706004146, "cookie_crate", false, 8719520066357431755], [16900715236047033623, "http_body_util", false, 10779507061208623305]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-afc6a4155b1c0323\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}