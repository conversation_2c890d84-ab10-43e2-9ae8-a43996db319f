{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 15174643492247994360, "deps": [[40386456601120721, "percent_encoding", false, 8048334745010741628], [442785307232013896, "tauri_runtime", false, 13549021302561219991], [1200537532907108615, "url<PERSON><PERSON>n", false, 3113689551287369002], [3150220818285335163, "url", false, 4415034812604799782], [4143744114649553716, "raw_window_handle", false, 16225817570159418652], [4341921533227644514, "muda", false, 4461929631421578139], [4919829919303820331, "serialize_to_javascript", false, 1879472185222832804], [5986029879202738730, "log", false, 7548405875655827093], [7752760652095876438, "tauri_runtime_wry", false, 16190867282781844003], [8351317599104215083, "tray_icon", false, 1782573486951315464], [8539587424388551196, "webview2_com", false, 14963465387744160124], [9010263965687315507, "http", false, 9745525180057106504], [9228235415475680086, "tauri_macros", false, 552847655289709843], [9538054652646069845, "tokio", false, 16960573143471379461], [9689903380558560274, "serde", false, 15866268910020275570], [9920160576179037441, "getrandom", false, 11929828218983572778], [10229185211513642314, "mime", false, 14694045163818787355], [10629569228670356391, "futures_util", false, 16259651420667836379], [10755362358622467486, "build_script_build", false, 11794583704759428693], [10806645703491011684, "thiserror", false, 11504436264092613361], [11050281405049894993, "tauri_utils", false, 13681551877324909697], [11989259058781683633, "dunce", false, 18200519651017601263], [12565293087094287914, "window_vibrancy", false, 8564843739176354693], [12986574360607194341, "serde_repr", false, 14773729372264842790], [13077543566650298139, "heck", false, 6461015136477599461], [13116089016666501665, "windows", false, 5463830761823751857], [13625485746686963219, "anyhow", false, 5041847958751135102], [15367738274754116744, "serde_json", false, 12735670442722202607], [16928111194414003569, "dirs", false, 2606562477705151498], [17155886227862585100, "glob", false, 12483713391891018660]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-67eeb0fb313f5370\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}