{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 16836410838408385390, "deps": [[500211409582349667, "shared_child", false, 126356092575777373], [1582828171158827377, "build_script_build", false, 9192057976439078898], [5986029879202738730, "log", false, 14896891622662359858], [9451456094439810778, "regex", false, 17734066832936030513], [9538054652646069845, "tokio", false, 11510013571635662308], [9689903380558560274, "serde", false, 17399218494941812765], [10755362358622467486, "tauri", false, 12606124176865590564], [10806645703491011684, "thiserror", false, 11478485744550063485], [11337703028400419576, "os_pipe", false, 11275586441473313267], [14564311161534545801, "encoding_rs", false, 14126388641811842202], [15367738274754116744, "serde_json", false, 5842069726811623790], [16192041687293812804, "open", false, 7667517625823622197]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-fa348d08de0a04fb\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}