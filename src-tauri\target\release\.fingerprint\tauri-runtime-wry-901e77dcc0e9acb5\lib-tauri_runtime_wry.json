{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 13590313119218913916, "deps": [[376837177317575824, "softbuffer", false, 17196404553924640523], [442785307232013896, "tauri_runtime", false, 13549021302561219991], [3150220818285335163, "url", false, 4415034812604799782], [3722963349756955755, "once_cell", false, 11157636835710291108], [4143744114649553716, "raw_window_handle", false, 16225817570159418652], [5986029879202738730, "log", false, 7548405875655827093], [7752760652095876438, "build_script_build", false, 7712743011749982509], [8539587424388551196, "webview2_com", false, 14963465387744160124], [9010263965687315507, "http", false, 9745525180057106504], [11050281405049894993, "tauri_utils", false, 13681551877324909697], [13116089016666501665, "windows", false, 5463830761823751857], [13223659721939363523, "tao", false, 8624493649559726828], [14794439852947137341, "wry", false, 11601750153077435891]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-901e77dcc0e9acb5\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}