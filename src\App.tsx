import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { windowApi } from './utils/tauri';
import { filterVideos } from './utils/youtube';
import { Video, Playlist, Mix } from './types';
import {
  useAppData,
  useMediaPlayer,
  useKeyboardShortcuts,
  useTauriEvents
} from './hooks';
import {
  Header,
  Sidebar,
  VideoPlayer,
  Controls,
  HelpModal,
  QuitConfirmation,
  Notification,
  LoadingSpinner,
  ErrorBoundary
} from './components';
import './App.css';

/**
 * Main Lightning Shuffler App Component
 * Refactored for better organization and maintainability
 */
function App() {
  // Local UI state
  const [currentVideo, setCurrentVideo] = useState<Video | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [showHelp, setShowHelp] = useState(false);
  const [showQuitConfirmation, setShowQuitConfirmation] = useState(false);
  const [loopCount, setLoopCount] = useState(0);

  // Custom hooks for data and functionality
  const {
    appData,
    isLoading,
    notification,
    addPlaylist,
    removePlaylist,
    refreshPlaylist,
    setQueueFromPlaylist,
    setQueueFromMix,
    setCurrentIndex,
    shuffleQueue
  } = useAppData();

  // Derived state
  const currentQueue = appData?.current_queue || [];
  const currentIndex = appData?.current_index || 0;
  const filteredVideos = filterVideos(currentQueue, searchQuery);

  // Set current video when app data changes
  useEffect(() => {
    if (appData && currentQueue.length > 0 && currentIndex < currentQueue.length) {
      setCurrentVideo(currentQueue[currentIndex]);
    } else {
      setCurrentVideo(null);
    }
  }, [appData, currentQueue, currentIndex]);

  // Set loop count from app data
  useEffect(() => {
    if (appData) {
      setLoopCount(appData.loop_count);
    }
  }, [appData]);

  // Media player hook
  const {
    isPlaying,
    volume,
    togglePlayPause,
    playNext,
    playPrevious,
    seekForward,
    seekBackward,
    toggleMute,
    setPlayerVolume,
    handleLoopClick,
    onPlayerReady,
    onPlayerStateChange
  } = useMediaPlayer({
    currentVideo,
    queue: currentQueue,
    currentIndex,
    loopCount,
    onVideoChange: (index) => setCurrentIndex(index),
    onLoopCountChange: setLoopCount
  });

  // Event handlers
  const handleAddPlaylist = async () => {
    const success = await addPlaylist(playlistUrl);
    if (success) {
      setPlaylistUrl('');
    }
  };

  const handlePlayPlaylist = async (playlist: Playlist) => {
    await setQueueFromPlaylist(playlist, appData?.shuffle_enabled || false);
  };

  const handlePlayMix = async (mix: Mix) => {
    await setQueueFromMix(mix, appData?.shuffle_enabled || false);
  };

  const handleVideoClick = async (video: Video) => {
    const videoIndex = currentQueue.findIndex(v => v.id === video.id);
    if (videoIndex !== -1) {
      await setCurrentIndex(videoIndex);
    }
  };

  const handleQuitApp = async () => {
    try {
      await windowApi.exit();
    } catch (error) {
      console.error('Failed to quit app:', error);
      window.close();
    }
  };

  // Setup keyboard shortcuts
  useKeyboardShortcuts({
    onPlayPause: togglePlayPause,
    onSeekForward: seekForward,
    onSeekBackward: seekBackward,
    onMute: toggleMute,
    onShowHelp: () => setShowHelp(true)
  });

  // Setup Tauri event listeners
  useTauriEvents({
    onPlayPause: togglePlayPause,
    onNext: playNext,
    onPrevious: playPrevious,
    onShowQuitConfirmation: () => setShowQuitConfirmation(true)
  });

  // Loading screen
  if (!appData) {
    return (
      <div className="app-container">
        <LoadingSpinner
          size="lg"
          text="Loading Lightning Shuffler..."
          variant="lightning"
        />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <motion.div
        className="app-container"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Sidebar */}
        <ErrorBoundary>
          <Sidebar
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            playlists={appData.playlists}
            mixes={appData.mixes}
            filteredVideos={filteredVideos}
            currentVideo={currentVideo}
            onPlayPlaylist={handlePlayPlaylist}
            onPlayMix={handlePlayMix}
            onRefreshPlaylist={refreshPlaylist}
            onDeletePlaylist={removePlaylist}
            onDeleteMix={() => { }} // TODO: Implement mix deletion
            onVideoClick={handleVideoClick}
          />
        </ErrorBoundary>

        {/* Main Content */}
        <div className="main-content">
          {/* Header */}
          <ErrorBoundary>
            <Header
              playlistUrl={playlistUrl}
              isLoading={isLoading}
              onPlaylistUrlChange={setPlaylistUrl}
              onAddPlaylist={handleAddPlaylist}
              onShowHelp={() => setShowHelp(true)}
            />
          </ErrorBoundary>

          {/* Video Player */}
          <ErrorBoundary>
            <VideoPlayer
              currentVideo={currentVideo}
              onReady={onPlayerReady}
              onStateChange={onPlayerStateChange}
            />
          </ErrorBoundary>

          {/* Controls */}
          <ErrorBoundary>
            <Controls
              isPlaying={isPlaying}
              loopCount={loopCount}
              volume={volume}
              onPlayPause={togglePlayPause}
              onPrevious={playPrevious}
              onNext={playNext}
              onShuffle={shuffleQueue}
              onLoopClick={handleLoopClick}
              onVolumeChange={setPlayerVolume}
            />
          </ErrorBoundary>
        </div>

        {/* Help Modal */}
        <HelpModal
          isOpen={showHelp}
          onClose={() => setShowHelp(false)}
        />

        {/* Quit Confirmation Dialog */}
        <QuitConfirmation
          isOpen={showQuitConfirmation}
          onConfirm={handleQuitApp}
          onCancel={() => setShowQuitConfirmation(false)}
        />

        {/* Notification */}
        <Notification notification={notification} />
      </motion.div>
    </ErrorBoundary>
  );
}

export default App;