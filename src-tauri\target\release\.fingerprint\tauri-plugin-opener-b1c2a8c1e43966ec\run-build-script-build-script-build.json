{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 11794583704759428693], [3935545708480822364, "build_script_build", false, 9567774776460599475]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-opener-b1c2a8c1e43966ec\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}