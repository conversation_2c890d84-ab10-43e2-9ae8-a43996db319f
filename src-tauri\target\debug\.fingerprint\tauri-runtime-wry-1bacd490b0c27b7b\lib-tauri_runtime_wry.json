{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 13590313119218913916, "deps": [[376837177317575824, "softbuffer", false, 8914422637730176809], [442785307232013896, "tauri_runtime", false, 871766587812999130], [3150220818285335163, "url", false, 10901662651222008182], [3722963349756955755, "once_cell", false, 196681822214180401], [4143744114649553716, "raw_window_handle", false, 2771300590775076479], [5986029879202738730, "log", false, 14896891622662359858], [7752760652095876438, "build_script_build", false, 11693721209338635425], [8539587424388551196, "webview2_com", false, 14483231888305795184], [9010263965687315507, "http", false, 16714832165723395293], [11050281405049894993, "tauri_utils", false, 12727919046660084077], [13116089016666501665, "windows", false, 111976557147647525], [13223659721939363523, "tao", false, 11414486741506952740], [14794439852947137341, "wry", false, 7477504541484745700]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-1bacd490b0c27b7b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}