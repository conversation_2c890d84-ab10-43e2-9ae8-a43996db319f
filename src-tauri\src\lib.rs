use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use tauri::menu::{Menu, MenuItem, PredefinedMenuItem};
use tauri::tray::{<PERSON><PERSON><PERSON><PERSON>, TrayIconBuilder, TrayIconEvent};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager, State, WindowEvent};
use tokio::sync::Mutex;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Video {
    pub id: String,
    pub title: String,
    pub author: String,
    pub thumbnail: String,
    pub duration: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Playlist {
    pub id: String,
    pub title: String,
    pub author: String,
    pub thumbnail: String,
    pub url: String,
    pub videos: Vec<Video>,
    pub video_count: usize,
    pub created_at: String,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct Mix {
    pub id: String,
    pub name: String,
    pub playlist_ids: Vec<String>,
    pub created_at: String,
}

#[derive(Debug, Serialize, Deserialize, De<PERSON>ult, <PERSON><PERSON>)]
pub struct AppData {
    pub playlists: HashMap<String, Playlist>,
    pub mixes: HashMap<String, Mix>,
    pub current_queue: Vec<Video>,
    pub current_index: usize,
    pub shuffle_enabled: bool,
    pub loop_count: u32,
    pub volume: f64,
}

type AppState = Mutex<AppData>;

#[tauri::command]
async fn get_app_data(state: State<'_, AppState>) -> Result<AppData, String> {
    let data = state.lock().await;
    Ok((*data).clone())
}

#[tauri::command]
async fn save_app_data(state: State<'_, AppState>, app_handle: AppHandle) -> Result<(), String> {
    let data = state.lock().await;
    save_data_to_file(&*data, &app_handle)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn extract_playlist_id(url: String) -> Result<String, String> {
    let playlist_regex = Regex::new(r"[&?]list=([a-zA-Z0-9_-]+)").unwrap();

    if let Some(captures) = playlist_regex.captures(&url) {
        if let Some(playlist_id) = captures.get(1) {
            return Ok(playlist_id.as_str().to_string());
        }
    }

    Err("Invalid YouTube playlist URL".to_string())
}

#[tauri::command]
async fn add_playlist(
    state: State<'_, AppState>,
    playlist: Playlist,
    app_handle: AppHandle,
) -> Result<(), String> {
    let mut data = state.lock().await;
    data.playlists.insert(playlist.id.clone(), playlist);
    drop(data);
    save_app_data(state, app_handle).await
}

#[tauri::command]
async fn remove_playlist(
    state: State<'_, AppState>,
    playlist_id: String,
    app_handle: AppHandle,
) -> Result<(), String> {
    let mut data = state.lock().await;
    data.playlists.remove(&playlist_id);
    drop(data);
    save_app_data(state, app_handle).await
}

#[tauri::command]
async fn create_mix(
    state: State<'_, AppState>,
    name: String,
    playlist_ids: Vec<String>,
    app_handle: AppHandle,
) -> Result<String, String> {
    let mix_id = uuid::Uuid::new_v4().to_string();
    let mix = Mix {
        id: mix_id.clone(),
        name,
        playlist_ids,
        created_at: chrono::Utc::now().to_rfc3339(),
    };

    let mut data = state.lock().await;
    data.mixes.insert(mix_id.clone(), mix);
    drop(data);
    save_app_data(state, app_handle).await?;
    Ok(mix_id)
}

#[tauri::command]
async fn set_current_queue(
    state: State<'_, AppState>,
    videos: Vec<Video>,
    shuffle: bool,
    app_handle: AppHandle,
) -> Result<(), String> {
    let mut data = state.lock().await;
    data.current_queue = if shuffle {
        let mut shuffled = videos;
        use rand::seq::SliceRandom;
        let mut rng = rand::thread_rng();
        shuffled.shuffle(&mut rng);
        shuffled
    } else {
        videos
    };
    data.current_index = 0;
    data.shuffle_enabled = shuffle;
    drop(data);
    save_app_data(state, app_handle).await
}

#[tauri::command]
async fn set_current_index(
    state: State<'_, AppState>,
    index: usize,
    app_handle: AppHandle,
) -> Result<(), String> {
    let mut data = state.lock().await;
    if index < data.current_queue.len() {
        data.current_index = index;
        drop(data);
        save_app_data(state, app_handle).await
    } else {
        Err("Index out of bounds".to_string())
    }
}

#[tauri::command]
async fn update_tray_tooltip(app_handle: AppHandle, tooltip: String) -> Result<(), String> {
    if let Some(tray) = app_handle.tray_by_id("main") {
        tray.set_tooltip(Some(tooltip)).map_err(|e| e.to_string())?;
    }
    Ok(())
}

#[tauri::command]
async fn show_window(app_handle: AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        window.show().map_err(|e| e.to_string())?;
        window.set_focus().map_err(|e| e.to_string())?;
    }
    Ok(())
}

#[tauri::command]
async fn hide_window(app_handle: AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        window.hide().map_err(|e| e.to_string())?;
    }
    Ok(())
}

#[tauri::command]
async fn show_quit_confirmation(app_handle: AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_webview_window("main") {
        // Show window first, then emit quit confirmation
        let _ = window.show();
        let _ = window.set_focus();
        let _ = window.emit("show-quit-confirmation", ());
    }
    Ok(())
}

#[tauri::command]
async fn exit_app(app_handle: AppHandle) -> Result<(), String> {
    // Clean up and exit
    app_handle.exit(0);
    Ok(())
}

async fn get_data_file_path(app_handle: &AppHandle) -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data_dir = app_handle.path().app_data_dir()?;
    fs::create_dir_all(&app_data_dir)?;
    Ok(app_data_dir.join("lightning_shuffler_data.json"))
}

async fn load_data_from_file(
    app_handle: &AppHandle,
) -> Result<AppData, Box<dyn std::error::Error>> {
    let file_path = get_data_file_path(app_handle).await?;

    if file_path.exists() {
        let content = fs::read_to_string(file_path)?;
        let data: AppData = serde_json::from_str(&content)?;
        Ok(data)
    } else {
        Ok(AppData::default())
    }
}

async fn save_data_to_file(
    data: &AppData,
    app_handle: &AppHandle,
) -> Result<(), Box<dyn std::error::Error>> {
    let file_path = get_data_file_path(app_handle).await?;
    let content = serde_json::to_string_pretty(data)?;
    fs::write(file_path, content)?;
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            let app_handle = app.handle().clone();

            // Initialize app state synchronously
            let data = tauri::async_runtime::block_on(async {
                load_data_from_file(&app_handle).await.unwrap_or_default()
            });
            app.manage(AppState::new(data));

            // Setup system tray
            let play_pause =
                MenuItem::with_id(app, "play_pause", "Play/Pause", true, None::<&str>)?;
            let next_track = MenuItem::with_id(app, "next", "Next Track", true, None::<&str>)?;
            let prev_track =
                MenuItem::with_id(app, "previous", "Previous Track", true, None::<&str>)?;
            let separator = PredefinedMenuItem::separator(app)?;
            let show_window = MenuItem::with_id(app, "show", "Show Window", true, None::<&str>)?;
            let quit = MenuItem::with_id(app, "quit", "Quit", true, None::<&str>)?;

            let menu = Menu::with_items(
                app,
                &[
                    &play_pause,
                    &next_track,
                    &prev_track,
                    &separator,
                    &show_window,
                    &quit,
                ],
            )?;

            let _tray = TrayIconBuilder::with_id("main")
                .icon(app.default_window_icon().unwrap().clone())
                .menu(&menu)
                .tooltip("Lightning Shuffler")
                .on_tray_icon_event(|tray, event| {
                    match event {
                        TrayIconEvent::Click { button, .. } => {
                            match button {
                                MouseButton::Left => {
                                    // Left click shows the window
                                    let app_handle = tray.app_handle();
                                    if let Some(window) = app_handle.get_webview_window("main") {
                                        let _ = window.show();
                                        let _ = window.set_focus();
                                    }
                                }
                                MouseButton::Right => {
                                    // Right click shows quit confirmation dialog directly
                                    let app_handle = tray.app_handle();
                                    if let Some(window) = app_handle.get_webview_window("main") {
                                        let _ = window.show();
                                        let _ = window.set_focus();
                                        let _ = window.emit("show-quit-confirmation", ());
                                    }
                                }
                                _ => {}
                            }
                        }
                        _ => {}
                    }
                })
                .on_menu_event(|app, event| {
                    match event.id().as_ref() {
                        "play_pause" => {
                            // Emit event to frontend
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.emit("tray-play-pause", ());
                            }
                        }
                        "next" => {
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.emit("tray-next", ());
                            }
                        }
                        "previous" => {
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.emit("tray-previous", ());
                            }
                        }
                        "show" => {
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.show();
                                let _ = window.set_focus();
                            }
                        }
                        "quit" => {
                            app.exit(0);
                        }
                        _ => {}
                    }
                })
                .build(app)?;

            // Handle window close event to hide to tray instead of closing
            if let Some(window) = app.get_webview_window("main") {
                let window_clone = window.clone();
                window.on_window_event(move |event| {
                    if let WindowEvent::CloseRequested { api, .. } = event {
                        api.prevent_close();
                        let _ = window_clone.hide();
                    }
                });
            }

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_app_data,
            save_app_data,
            extract_playlist_id,
            add_playlist,
            remove_playlist,
            create_mix,
            set_current_queue,
            set_current_index,
            update_tray_tooltip,
            show_window,
            hide_window,
            show_quit_confirmation,
            exit_app
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
