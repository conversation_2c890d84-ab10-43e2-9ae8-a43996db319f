{"rustc": 16591470773350601817, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 2040997289075261528, "path": 893707485121594417, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3113689551287369002], [3150220818285335163, "url", false, 4415034812604799782], [8298091525883606470, "cookie_store", false, 4078074362396950370], [9010263965687315507, "http", false, 9745525180057106504], [9451456094439810778, "regex", false, 605544959772061804], [9538054652646069845, "tokio", false, 16960573143471379461], [9689903380558560274, "serde", false, 15866268910020275570], [10755362358622467486, "tauri", false, 1320775132830790491], [10806645703491011684, "thiserror", false, 11504436264092613361], [13890802266741835355, "tauri_plugin_fs", false, 14745794600997847518], [15367738274754116744, "serde_json", false, 12735670442722202607], [15441187897486245138, "build_script_build", false, 11509775261427612086], [16066129441945555748, "bytes", false, 6037974485521700484], [16593743942913858012, "reqwest", false, 15087967984295122511], [17047088963840213854, "data_url", false, 1741575058610166461]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-http-1e052e481297215b\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}