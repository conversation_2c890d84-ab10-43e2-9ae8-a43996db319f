["\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\Users\\<USER>\\Downloads\\projects\\Other\\Lightning Shuffler Tauri\\src-tauri\\target\\debug\\build\\tauri-7467641447d34b85\\out\\permissions\\path\\autogenerated\\default.toml"]