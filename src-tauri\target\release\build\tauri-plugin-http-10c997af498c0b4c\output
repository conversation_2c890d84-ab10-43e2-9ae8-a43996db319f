cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler Tauri\src-tauri\target\release\build\tauri-plugin-http-10c997af498c0b4c\out\tauri-plugin-http-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler Tauri\src-tauri\target\release\build\tauri-plugin-http-10c997af498c0b4c\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-http-2.4.4\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
